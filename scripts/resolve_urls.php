<?php
/**
 * Product Hunt URL Resolver
 * Resolves external URLs for products in the database
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database_setup.php';
require_once __DIR__ . '/../includes/url_utils.php';

class URLResolver {
    private $pdo;
    private $options = [];
    private $targetDate = null;
    private $startDate = null;
    private $endDate = null;
    private $limit = null;
    private $force = false;
    private $all = false;
    private $recheckDown = false;
    private $usePlaywright = true; // Default to true for fallback
    private $playwrightAvailable = false;

    public function __construct($options = []) {
        $this->options = $options;
        validateConfig();
        $this->connectToDatabase();

        // Set date filters
        if (isset($options['date'])) {
            $this->targetDate = $options['date'];
        } elseif (isset($options['start_date']) && isset($options['end_date'])) {
            $this->startDate = $options['start_date'];
            $this->endDate = $options['end_date'];
        }

        // Set other options
        $this->force = isset($options['force']) && $options['force'];
        $this->all = isset($options['all']) && $options['all'];
        $this->recheckDown = isset($options['recheck_down']) && $options['recheck_down'];

        if (isset($options['limit']) && $options['limit'] > 0) {
            $this->limit = $options['limit'];
            echo "🔢 Limiting URL resolution to {$this->limit} products\n";
        }

        // Always check Playwright availability for fallback
        $this->checkPlaywrightAvailability();
    }
    
    private function connectToDatabase() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $this->pdo = new PDO($dsn, DB_USER, DB_PASSWORD, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    private function checkPlaywrightAvailability() {
        // Check if Node.js is available
        $nodeCheck = shell_exec('which node 2>/dev/null');
        if (empty($nodeCheck)) {
            echo "⚠️  Node.js not found. Playwright fallback disabled.\n";
            $this->playwrightAvailable = false;
            return;
        }

        // Check if npx is available
        $npxCheck = shell_exec('which npx 2>/dev/null');
        if (empty($npxCheck)) {
            echo "⚠️  npx not found. Playwright fallback disabled.\n";
            $this->playwrightAvailable = false;
            return;
        }

        // Check if Playwright is installed
        $playwrightCheck = shell_exec('npx playwright --version 2>/dev/null');
        if (empty($playwrightCheck)) {
            echo "⚠️  Playwright not found. Installing...\n";
            $this->installPlaywright();
        } else {
            echo "✅ Playwright available: " . trim($playwrightCheck) . "\n";
            $this->playwrightAvailable = true;
        }
    }

    private function installPlaywright() {
        echo "📦 Installing Playwright...\n";
        $installCmd = 'npx playwright install chromium 2>&1';
        $output = shell_exec($installCmd);

        if (strpos($output, 'error') !== false || strpos($output, 'Error') !== false) {
            echo "❌ Failed to install Playwright. Fallback disabled.\n";
            $this->playwrightAvailable = false;
        } else {
            echo "✅ Playwright installed successfully\n";
            $this->playwrightAvailable = true;
        }
    }

    private function resolveUrlWithPlaywright($url) {
        if (!$this->playwrightAvailable) {
            return ['success' => false, 'error' => 'Playwright not available'];
        }

        echo "🎭 Using Playwright fallback for: $url\n";

        // Create a temporary JavaScript file for Playwright
        $jsScript = $this->createPlaywrightScript($url);
        $tempFile = tempnam(sys_get_temp_dir(), 'playwright_') . '.js';
        file_put_contents($tempFile, $jsScript);

        try {
            // Run Playwright with the script
            $cmd = "npx playwright test --config=/dev/null --reporter=json " . escapeshellarg($tempFile) . " 2>&1";
            $output = shell_exec($cmd);

            // Parse the result
            $result = $this->parsePlaywrightOutput($output);

            return $result;

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        } finally {
            // Clean up temp file
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    private function createPlaywrightScript($url) {
        return "
const { test, expect } = require('@playwright/test');

test('resolve url', async ({ page }) => {
    try {
        // Set realistic user agent
        await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        // Set viewport
        await page.setViewportSize({ width: 1920, height: 1080 });

        // Navigate to URL with timeout
        const response = await page.goto('" . addslashes($url) . "', {
            waitUntil: 'networkidle',
            timeout: 15000
        });

        // Get final URL after redirects
        const finalUrl = page.url();
        const statusCode = response.status();

        // Determine status
        const status = (statusCode >= 200 && statusCode < 400) ? 'up' : 'down';

        // Output result as JSON
        console.log(JSON.stringify({
            success: true,
            finalUrl: finalUrl,
            statusCode: statusCode,
            status: status
        }));

    } catch (error) {
        console.log(JSON.stringify({
            success: false,
            error: error.message
        }));
    }
});
";
    }

    private function parsePlaywrightOutput($output) {
        // Look for JSON output in the Playwright output
        $lines = explode("\n", $output);

        foreach ($lines as $line) {
            $line = trim($line);
            if (strpos($line, '{') === 0) {
                $json = json_decode($line, true);
                if ($json !== null) {
                    return $json;
                }
            }
        }

        return [
            'success' => false,
            'error' => 'Could not parse Playwright output: ' . substr($output, 0, 200)
        ];
    }

    public function resolveUrls() {
        $products = $this->getProductsToResolve();
        
        if (empty($products)) {
            echo "ℹ️  No products found that need URL resolution.\n";
            return 0;
        }

        echo "Found " . count($products) . " products that need URL resolution:\n\n";

        $processed = 0;
        $successful = 0;
        $failed = 0;

        foreach ($products as $product) {
            echo "🔗 Processing: " . $product['name'] . "\n";

            // Update the updated_at timestamp first
            $updateStmt = $this->pdo->prepare("UPDATE products SET updated_at = NOW() WHERE id = ?");
            $updateStmt->execute([$product['id']]);

            // Process URL resolution
            if ($product['url']) {
                $result = $this->resolveExternalUrl($product['id']);
                if ($result) {
                    $successful++;
                } else {
                    $failed++;
                }
                $processed++;
            }
        }

        echo "\n📊 URL Resolution Results:\n";
        echo "- Products processed: {$processed}\n";
        echo "- Successfully resolved: {$successful}\n";
        echo "- Failed to resolve: {$failed}\n";

        return $processed;
    }

    private function getProductsToResolve() {
        $whereConditions = [];
        $params = [];

        // Date filtering
        if ($this->targetDate) {
            $whereConditions[] = "DATE(date_created) = ?";
            $params[] = $this->targetDate;
        } elseif ($this->startDate && $this->endDate) {
            $whereConditions[] = "DATE(date_created) BETWEEN ? AND ?";
            $params[] = $this->startDate;
            $params[] = $this->endDate;
        }

        // URL resolution filtering
        if ($this->force) {
            // Force mode: resolve all URLs (no additional filtering)
        } elseif ($this->recheckDown) {
            // Recheck down mode: only resolve products where external_url_status is 'down'
            $whereConditions[] = "external_url_status = 'down'";
        } else {
            // Default: only resolve products where both external_url and external_url_status are null
            $whereConditions[] = "external_url IS NULL AND external_url_status IS NULL";
        }

        // Build the WHERE clause
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }

        // Order by clause
        $orderClause = $this->all ? 
            'ORDER BY date_created DESC, votes_count DESC' : 
            'ORDER BY date_created DESC, votes_count DESC';

        // Limit clause
        $limitClause = $this->limit ? "LIMIT {$this->limit}" : '';

        $sql = "
            SELECT id, name, url, external_url, external_url_status
            FROM products
            {$whereClause}
            {$orderClause}
            {$limitClause}
        ";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    private function resolveExternalUrl($productId) {
        try {
            // Get the current URL for this product
            $stmt = $this->pdo->prepare("SELECT url FROM products WHERE id = ?");
            $stmt->execute([$productId]);
            $result = $stmt->fetch();

            if (!$result || empty($result['url'])) {
                return false; // No URL to resolve
            }

            $originalUrl = $result['url'];

            // Check if this URL is likely a redirect
            if (!URLUtils::isLikelyRedirect($originalUrl)) {
                // Not a redirect URL, mark as valid and store cleaned original URL
                $cleanedUrl = URLUtils::removeUrlParameters($originalUrl);
                $stmt = $this->pdo->prepare("UPDATE products SET external_url = ?, external_url_status = 'up' WHERE id = ?");
                $stmt->execute([$cleanedUrl, $productId]);
                echo "✅ Direct URL (no redirect): " . substr($cleanedUrl, 0, 50) . "...\n";
                return true;
            }

            // Follow redirects to get the final URL
            echo "🔗 Resolving redirects for: " . substr($originalUrl, 0, 50) . "...\n";
            $finalUrl = URLUtils::getFinalUrl($originalUrl);

            if (!empty($finalUrl)) {
                // Successfully resolved - clean the URL before storing
                $cleanedFinalUrl = URLUtils::removeUrlParameters($finalUrl);
                $stmt = $this->pdo->prepare("UPDATE products SET external_url = ?, external_url_status = 'up' WHERE id = ?");
                $stmt->execute([$cleanedFinalUrl, $productId]);
                echo "✅ Resolved to: " . substr($cleanedFinalUrl, 0, 50) . "...\n";
                return true;
            } else {
                // cURL failed - try Playwright fallback if available
                if ($this->usePlaywright && $this->playwrightAvailable) {
                    echo "🎭 Trying Playwright fallback...\n";

                    // Add delay to be respectful
                    sleep(rand(3, 6));

                    $playwrightResult = $this->resolveUrlWithPlaywright($originalUrl);

                    if ($playwrightResult['success'] && isset($playwrightResult['status'])) {
                        if ($playwrightResult['status'] === 'up') {
                            $cleanedFinalUrl = URLUtils::removeUrlParameters($playwrightResult['finalUrl']);
                            $stmt = $this->pdo->prepare("UPDATE products SET external_url = ?, external_url_status = 'up' WHERE id = ?");
                            $stmt->execute([$cleanedFinalUrl, $productId]);
                            echo "✅ Playwright resolved to: " . substr($cleanedFinalUrl, 0, 50) . "...\n";
                            return true;
                        } else {
                            $stmt = $this->pdo->prepare("UPDATE products SET external_url = NULL, external_url_status = 'down' WHERE id = ?");
                            $stmt->execute([$productId]);
                            echo "❌ Playwright confirmed URL is down\n";
                            return false;
                        }
                    } else {
                        echo "⚠️ Playwright fallback failed: " . ($playwrightResult['error'] ?? 'Unknown error') . "\n";
                    }
                }

                // Failed to resolve (404, timeout, etc.) - no fallback or fallback failed
                $stmt = $this->pdo->prepare("UPDATE products SET external_url = NULL, external_url_status = 'down' WHERE id = ?");
                $stmt->execute([$productId]);
                echo "❌ Failed to resolve URL\n";
                return false;
            }

        } catch (Exception $e) {
            echo "⚠️ Error resolving URL for product $productId: " . $e->getMessage() . "\n";
            return false;
        }
    }
}

// Helper function to parse command line arguments
function parseArguments($argv) {
    $options = [];

    for ($i = 1; $i < count($argv); $i++) {
        $arg = $argv[$i];

        if (strpos($arg, '--date=') === 0) {
            $options['date'] = substr($arg, 7);
        } elseif (strpos($arg, '--start-date=') === 0) {
            $options['start_date'] = substr($arg, 13);
        } elseif (strpos($arg, '--end-date=') === 0) {
            $options['end_date'] = substr($arg, 11);
        } elseif (strpos($arg, '--limit=') === 0) {
            $options['limit'] = (int)substr($arg, 8);
        } elseif ($arg === '--force') {
            $options['force'] = true;
        } elseif ($arg === '--all') {
            $options['all'] = true;
        } elseif ($arg === '--recheck-down') {
            $options['recheck_down'] = true;
        } elseif ($arg === '--help' || $arg === '-h') {
            echo "Usage: php resolve_urls.php [OPTIONS]\n\n";
            echo "Options:\n";
            echo "  --date=YYYY-MM-DD          Resolve URLs for products from specific date\n";
            echo "  --start-date=YYYY-MM-DD    Resolve URLs for products from date range (requires --end-date)\n";
            echo "  --end-date=YYYY-MM-DD      Resolve URLs for products to date range (requires --start-date)\n";
            echo "  --limit=N                  Limit URL resolution to N products\n";
            echo "  --force                    Resolve all URLs (not just unresolved ones)\n";
            echo "  --recheck-down             Recheck URLs that previously failed (status = 'down')\n";
            echo "  --all                      Resolve URLs by created date descending (from newest to oldest)\n";
            echo "  --help, -h                 Show this help message\n\n";
            echo "Note: Playwright fallback is automatically used when cURL fails to resolve URLs.\n\n";
            echo "Examples:\n";
            echo "  php resolve_urls.php                           # Resolve completely unresolved URLs (default)\n";
            echo "  php resolve_urls.php --date=2024-01-15         # Resolve URLs for products from Jan 15, 2024\n";
            echo "  php resolve_urls.php --recheck-down --limit=25 # Recheck 25 URLs that previously failed\n";
            echo "  php resolve_urls.php --force --limit=50        # Force resolve 50 URLs (including already resolved)\n";
            echo "  php resolve_urls.php --all --limit=100         # Resolve 100 URLs starting from newest products\n";
            echo "  php resolve_urls.php --start-date=2024-01-01 --end-date=2024-01-31  # Resolve URLs for January 2024\n";
            exit(0);
        }
    }

    return $options;
}

// Run the URL resolver if this file is executed directly
if (basename(__FILE__) == basename($_SERVER["SCRIPT_NAME"])) {
    $startTime = microtime(true);
    $startDateTime = date('Y-m-d H:i:s');

    $options = parseArguments($argv ?? []);

    echo "🔗 Starting Product Hunt URL resolution...\n";
    echo "⏰ Start time: $startDateTime\n";

    // Display filtering info
    if (isset($options['date'])) {
        echo "📅 Resolving URLs for products from: {$options['date']}\n";
    } elseif (isset($options['start_date']) && isset($options['end_date'])) {
        echo "📅 Resolving URLs for products from: {$options['start_date']} to {$options['end_date']}\n";
    } else {
        echo "📅 Resolving URLs for all products\n";
    }

    if (isset($options['force']) && $options['force']) {
        echo "🔄 Force mode: Resolving all URLs (including already resolved)\n";
    } elseif (isset($options['recheck_down']) && $options['recheck_down']) {
        echo "🔍 Recheck mode: Only rechecking URLs that previously failed (status = 'down')\n";
    } else {
        echo "🎯 Default mode: Only resolving completely unresolved URLs (both external_url and status are null)\n";
    }

    if (isset($options['all']) && $options['all']) {
        echo "📊 Processing products from newest to oldest\n";
    }

    echo "\n";

    try {
        $resolver = new URLResolver($options);
        $processed = $resolver->resolveUrls();

        $endTime = microtime(true);
        $endDateTime = date('Y-m-d H:i:s');
        $duration = $endTime - $startTime;
        $durationFormatted = gmdate('H:i:s', (int)$duration);

        echo "\n⏰ End time: $endDateTime\n";
        echo "⏱️  Total duration: $durationFormatted (" . number_format($duration, 2) . " seconds)\n";
        echo "\n✨ URL resolution completed!\n";

    } catch (Exception $e) {
        $endTime = microtime(true);
        $endDateTime = date('Y-m-d H:i:s');
        $duration = $endTime - $startTime;
        $durationFormatted = gmdate('H:i:s', (int)$duration);

        echo "\n⏰ End time: $endDateTime\n";
        echo "⏱️  Total duration: $durationFormatted (" . number_format($duration, 2) . " seconds)\n";
        echo "❌ Error: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>
