<?php
/**
 * Product Hunt Products Display
 * Web interface for viewing and filtering products
 */

require_once 'config/config.php';
require_once 'includes/auth.php';

// Require authentication
Auth::requireAuth();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Hunt Dashboard</title>
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="https://<?php echo $_SERVER['HTTP_HOST']; ?>/assets/img/favicon.png">

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 13px;
        }
        .container {
            max-width: none;
        }
        h1 {
            text-align: left;
            font-size: 24px;
        }
        .stats {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .stats-left {
            display: flex;
            align-items: center;
        }

        /* Make table horizontally scrollable */
        .dataTables_wrapper {
            overflow-x: auto;
            width: 100%;
        }

        .dataTables_scroll {
            overflow-x: auto;
        }

        .dataTables_scrollBody {
            overflow-x: auto !important;
        }

        table.dataTable {
            width: 100% !important;
            white-space: nowrap;
            font-size: 13px;
            table-layout: fixed !important;
        }

        table.dataTable td,
        table.dataTable th {
            vertical-align: top !important;
        }

        /* Force fixed column widths */
        table.dataTable th:nth-child(1), table.dataTable td:nth-child(1) { width: 150px !important; max-width: 150px !important; font-weight: bold !important; }
        table.dataTable th:nth-child(2), table.dataTable td:nth-child(2) { width: 120px !important; max-width: 120px !important; }

        /* Manual URL popup styles */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .popup-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 1px solid #ccc;
            width: 450px;
            max-width: 90%;
            font-family: Arial, sans-serif;
            font-size: 13px;
        }

        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            margin: 0;
        }

        .popup-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
        }

        .popup-close {
            cursor: pointer;
            font-size: 20px;
            font-weight: bold;
            color: #999;
            line-height: 1;
            padding: 0;
            background: none;
            border: none;
        }

        .popup-close:hover {
            color: #333;
        }

        .popup-form {
            padding: 20px;
        }

        .popup-form label {
            display: block;
            margin-bottom: 8px;
            font-weight: normal;
        }

        .popup-form input[type="url"] {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ccc;
            margin-bottom: 20px;
            font-size: 13px;
            box-sizing: border-box;
        }

        .popup-buttons {
            text-align: right;
            margin: 0;
        }

        .popup-buttons button {
            margin-left: 8px;
            padding: 8px 16px;
            border: 1px solid #ccc;
            background: #f0f0f0;
            cursor: pointer;
            font-size: 13px;
        }

        .popup-buttons button:hover {
            background: #e0e0e0;
        }

        .manual-url-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 8px;
            margin: 5px 0;
            font-size: 12px;
        }

        /* Temporary notice styles */
        .temp-notice {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #f8f9fa;
            border: 1px solid #ccc;
            padding: 12px 16px;
            font-size: 13px;
            font-family: Arial, sans-serif;
            z-index: 1001;
            max-width: 300px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .temp-notice.show {
            opacity: 1;
            transform: translateY(0);
        }

        .temp-notice.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .temp-notice.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        table.dataTable th:nth-child(3), table.dataTable td:nth-child(3) { width: 200px !important; max-width: 200px !important; }
        table.dataTable th:nth-child(4), table.dataTable td:nth-child(4) { width: 60px !important; max-width: 60px !important; }
        table.dataTable th:nth-child(5), table.dataTable td:nth-child(5) { width: 60px !important; max-width: 60px !important; }
        table.dataTable th:nth-child(6), table.dataTable td:nth-child(6) { width: 90px !important; max-width: 90px !important; }
        table.dataTable th:nth-child(7), table.dataTable td:nth-child(7) { width: 120px !important; max-width: 120px !important; }
        table.dataTable th:nth-child(8), table.dataTable td:nth-child(8) { width: 180px !important; max-width: 180px !important; }
        table.dataTable th:nth-child(9), table.dataTable td:nth-child(9) { width: 90px !important; max-width: 90px !important; }
        table.dataTable th:nth-child(10), table.dataTable td:nth-child(10) { width: 90px !important; max-width: 90px !important; }

        /* Text truncation for long content */
        .text-truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .text-center {
            text-align: center;
        }

        /* URL link styling to prevent overflow */
        .url-link {
            word-break: break-all;
            overflow-wrap: break-word;
            hyphens: auto;
            max-width: 100%;
            display: inline-block;
        }

        /* Specific styling for Links column */
        table.dataTable th:nth-child(8),
        table.dataTable td:nth-child(8) {
            word-break: break-all;
            overflow-wrap: break-word;
            white-space: normal !important;
        }

        /* Remove text truncation - show all data */
        .product-description,
        .categories-list,
        .product-tagline {
            white-space: normal;
            word-wrap: break-word;
            min-width: 200px;
        }

        .product-tagline {
            min-width: 150px;
            max-width: 200px;
        }

        .url-link {
            color: #0066cc;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h1 style="margin: 0;"><a href="https://<?php echo $_SERVER['HTTP_HOST']; ?>" style="text-decoration: none; color: inherit;">Product Hunt Dashboard</a></h1>
            <div style="display: flex; align-items: center; gap: 15px;">
                <span>Welcome, <?= htmlspecialchars(Auth::getUsername(), ENT_QUOTES, 'UTF-8') ?></span>
                <a href="logout.php?token=<?= htmlspecialchars(Auth::generateCSRFToken(), ENT_QUOTES, 'UTF-8') ?>" style="padding: 4px 8px; text-decoration: none; border: 1px solid #ccc; color: #333; background: #f0f0f0; font-size: 13px;">
                   Logout
                </a>
            </div>
        </div>
        
        <?php
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $pdo = new PDO($dsn, DB_USER, DB_PASSWORD, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            
            // Get statistics
            $statsStmt = $pdo->query("
                SELECT
                    COUNT(*) as total_products,
                    COUNT(DISTINCT date_created) as unique_dates,
                    MIN(date_created) as oldest_fetch_date,
                    MAX(date_created) as newest_fetch_date
                FROM products
            ");
            $stats = $statsStmt->fetch();

            // Get available dates for the date picker
            $datesStmt = $pdo->prepare("SELECT DISTINCT DATE(date_created) as date FROM products ORDER BY date DESC");
            $datesStmt->execute();
            $availableDates = $datesStmt->fetchAll(PDO::FETCH_COLUMN);

            // Get all products with their categories and manual URL overrides
            $sql = "
                SELECT
                    p.*,
                    GROUP_CONCAT(c.name ORDER BY c.name SEPARATOR ', ') as categories,
                    muo.manual_url,
                    muo.created_at as manual_url_created_at
                FROM products p
                LEFT JOIN product_categories pc ON p.id = pc.product_id
                LEFT JOIN categories c ON pc.category_id = c.id
                LEFT JOIN manual_url_overrides muo ON p.id = muo.product_id
                GROUP BY p.id
                ORDER BY p.votes_count DESC
            ";
            $stmt = $pdo->query($sql);
            $products = $stmt->fetchAll();
            
        } catch (PDOException $e) {
            echo "<div style='color: red; text-align: center;'>Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
            exit;
        }
        ?>
        
        <!-- Statistics -->
        <div class="stats">
            <div class="stats-left">
                <span>Total Products: <?= number_format($stats['total_products']) ?></span>
                <span style="margin: 0 10px; color: #ccc;">|</span>
                <span>Launch Days: <?= $stats['unique_dates'] ?></span>
                <span style="margin: 0 10px; color: #ccc;">|</span>
                <span>Newest Fetch: <?= $stats['newest_fetch_date'] ? date('M j, Y', strtotime($stats['newest_fetch_date'])) : '-' ?></span>
                <span style="margin: 0 10px; color: #ccc;">|</span>
                <span>Oldest Fetch: <?= $stats['oldest_fetch_date'] ? date('M j, Y', strtotime($stats['oldest_fetch_date'])) : '-' ?></span>
            </div>
            <div class="live-time">
                <span id="localTime">Loading...</span>
                <span style="margin: 0 10px; color: #ccc;">|</span>
                <span id="pacificTime">Loading...</span>
            </div>
        </div>

        <!-- Status Filters -->
        <div class="filter-container" style="margin: 0 0 15px;">
            <strong>URL Filters:</strong>
            <a href="javascript:void(0);" id="filterAll" class="filter-link active" style="margin: 0 5px; color: #333; text-decoration: underline;">All Products</a>
            <a href="javascript:void(0);" id="filterFailed" class="filter-link" style="margin: 0 5px; color: #dc3545; text-decoration: none;">Failed URLs</a>
            <a href="javascript:void(0);" id="filterFixed" class="filter-link" style="margin: 0 5px; color: #28a745; text-decoration: none;">Fixed URLs</a>
            <a href="javascript:void(0);" id="filterUnprocessed" class="filter-link" style="margin: 0 5px; color: #6c757d; text-decoration: none;">Unprocessed URLs</a>
            <span style="margin: 0 10px; color: #ccc;">|</span>
            <strong style="margin: 0 5px">Filter by date:</strong>
            <input type="date" id="datePicker" style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 3px; margin: 0 5px;">
            <a href="javascript:void(0);" id="resetDateFilter" style="margin: 0 5px; color: #333; text-decoration: none;">Reset</a>
        </div>

        <!-- Products DataTable -->
        <table id="productsTable" class="display" style="width:100%">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Tagline</th>
                    <th>Description</th>
                    <th>Votes</th>
                    <th>Rating</th>
                    <th>Launch Date</th>
                    <th>Categories</th>
                    <th>Links</th>
                    <th>Created</th>
                    <th>Updated</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($products as $product): ?>
                <tr>
                    <td>
                        <?= htmlspecialchars($product['name']) ?>
                    </td>
                    <td>
                        <div class="product-tagline">
                            <?= $product['tagline'] ? htmlspecialchars($product['tagline']) : '-' ?>
                        </div>
                    </td>
                    <td>
                        <div class="product-description">
                            <?= $product['description'] ? htmlspecialchars($product['description']) : '-' ?>
                        </div>
                    </td>
                    <td>
                        <?= number_format($product['votes_count']) ?>
                    </td>
                    <td>
                        <?php if ($product['review_rating']): ?>
                            <?= number_format($product['review_rating'], 1) ?>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td>
                        <?= $product['date_created'] ? date('M j, Y', strtotime($product['date_created'])) : '-' ?>
                    </td>
                    <td>
                        <div class="categories-list">
                            <?= $product['categories'] ? htmlspecialchars($product['categories']) : '-' ?>
                        </div>
                    </td>
                    <td>
                        <?php if ($product['product_url']): ?>
                            Product Hunt URL:<br>
                            <a href="<?= htmlspecialchars($product['product_url']) ?>" target="_blank" class="url-link"><?= htmlspecialchars($product['product_url']) ?></a>
                            <br><br>
                        <?php endif; ?>

                        Website URL:<br>
                        <?php if ($product['manual_url']): ?>
                            <!-- Manual URL override exists -->
                            <a href="<?= htmlspecialchars($product['manual_url']) ?>" target="_blank" class="url-link"><?= htmlspecialchars($product['manual_url']) ?></a>
                            <span style="color: green; margin-left: 5px;">✓</span>
                            <a href="javascript:void(0);" onclick="removeManualUrl('<?= htmlspecialchars($product['id']) ?>')" style="margin-left: 5px; font-size: 12px; color: #666;">remove override</a>
                            <?php if ($product['external_url'] && $product['external_url_status'] === 'up'): ?>
                                <div class="manual-url-notice">
                                    URL resolved automatically.
                                </div>
                            <?php endif; ?>
                        <?php elseif ($product['external_url']): ?>
                            <a href="<?= htmlspecialchars($product['external_url']) ?>" target="_blank" class="url-link"><?= htmlspecialchars($product['external_url']) ?></a>
                            <?php if ($product['external_url_status'] === 'down'): ?>
                                <span style="color: red; margin-left: 5px;">✗</span>
                                <a href="<?= htmlspecialchars($product['url']) ?>" target="_blank" class="url-link" style="margin-left: 5px;">(original)</a>
                            <?php endif; ?>
                        <?php elseif ($product['external_url_status'] === 'down'): ?>
                            <span style="font-size: 9px;">❌</span>
                            <a href="<?= htmlspecialchars($product['url']) ?>" target="_blank" class="url-link" style="margin-left: 5px;">(original)</a>
                            <a href="javascript:void(0);" onclick="openManualUrlPopup('<?= htmlspecialchars($product['id']) ?>')" style="margin-left: 5px;">add link</a>
                        <?php else: ?>
                            <span style="font-size: 9px;">🕓</span>
                            <a href="<?= htmlspecialchars($product['url']) ?>" target="_blank" class="url-link" style="margin-left: 5px;">(original)</a>
                            <a href="javascript:void(0);" onclick="openManualUrlPopup('<?= htmlspecialchars($product['id']) ?>')" style="margin-left: 5px;">add link</a>
                        <?php endif; ?>

                        <?php if (!$product['product_url'] && !$product['external_url'] && $product['external_url_status'] !== 'down'): ?>
                            <br>-
                        <?php endif; ?>
                    </td>
                    <td>
                        <?= $product['created_at'] ? date('M j, Y H:i', strtotime($product['created_at'])) : '-' ?>
                    </td>
                    <td>
                        <?= $product['updated_at'] ? date('M j, Y H:i', strtotime($product['updated_at'])) : '-' ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Manual URL Popup -->
    <div id="manualUrlPopup" class="popup-overlay">
        <div class="popup-content">
            <div class="popup-header">
                <h3>Add Manual URL</h3>
                <span class="popup-close" onclick="closeManualUrlPopup()">&times;</span>
            </div>
            <form id="manualUrlForm" class="popup-form">
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars(Auth::generateCSRFToken(), ENT_QUOTES, 'UTF-8') ?>">
                <input type="hidden" name="product_id" id="popup_product_id">
                <input type="hidden" name="action" value="save">

                <label for="manual_url">Website URL:</label>
                <input type="url" name="manual_url" id="manual_url" placeholder="https://example.com" required>

                <div class="popup-buttons">
                    <button type="button" onclick="closeManualUrlPopup()">Cancel</button>
                    <button type="submit">Save</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Temporary Notice Container -->
    <div id="tempNotice" class="temp-notice"></div>

    <!-- jQuery and DataTables JS -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    
    <script>
        // Available dates from PHP
        var availableDates = <?= json_encode($availableDates) ?>;
        var currentDateFilter = null;

        $(document).ready(function() {
            var table = $('#productsTable').DataTable({
                "pageLength": parseInt(getUrlParam('length')) || 7,
                "lengthMenu": [[7, 10, 15, 25, 50, 100, -1], [7, 10, 15, 25, 50, 100, "All"]],
                "order": [[5, "desc"], [3, "desc"]], // Sort by Launch Date (column 5) desc, then Votes (column 3) desc
                "displayStart": parseInt(getUrlParam('start')) || 0,
                "search": {
                    "search": getUrlParam('search') || ''
                },
                "stateSave": false, // We'll handle state manually
                "columnDefs": [
                    { "orderable": false, "targets": [7] }, // Disable sorting on Links column
                    { "width": "150px", "targets": [0], "className": "text-truncate" }, // Product name
                    { "width": "120px", "targets": [1], "className": "text-truncate" }, // Tagline
                    { "width": "200px", "targets": [2], "className": "text-truncate" }, // Description
                    { "width": "60px", "targets": [3], "className": "text-center" }, // Votes
                    { "width": "60px", "targets": [4], "className": "text-center" }, // Rating
                    { "width": "90px", "targets": [5], "className": "text-center" }, // Launch Date
                    { "width": "120px", "targets": [6], "className": "text-truncate" }, // Categories
                    { "width": "180px", "targets": [7] }, // Links
                    { "width": "90px", "targets": [8], "className": "text-center" }, // Created
                    { "width": "90px", "targets": [9], "className": "text-center" }  // Updated
                ],
                "autoWidth": false,
                "fixedColumns": true,
                "scrollX": true, // Enable horizontal scrolling
                "autoWidth": false, // Let content determine width
                "language": {
                    "search": "Search products:",
                    "lengthMenu": "Show _MENU_ products per page",
                    "info": "Showing _START_ to _END_ of _TOTAL_ products",
                    "infoEmpty": "No products found",
                    "infoFiltered": "(filtered from _MAX_ total products)"
                }
            });

            // Restore state from URL parameters on page load
            function restoreStateFromUrl() {
                isRestoringState = true; // Prevent URL updates during restoration

                // Restore filter state first (this may reset pagination)
                var filterParam = getUrlParam('filter');
                if (filterParam === 'failed') {
                    // Apply search and update UI
                    table.column(7).search('❌').draw(false);
                    $('.filter-link').removeClass('active').css('text-decoration', 'none');
                    $('#filterFailed').addClass('active').css('text-decoration', 'underline');
                } else if (filterParam === 'fixed') {
                    // Apply search and update UI
                    table.column(7).search('✅').draw(false);
                    $('.filter-link').removeClass('active').css('text-decoration', 'none');
                    $('#filterFixed').addClass('active').css('text-decoration', 'underline');
                } else if (filterParam === 'unprocessed') {
                    // Apply search and update UI
                    table.column(7).search('unprocessed').draw(false);
                    $('.filter-link').removeClass('active').css('text-decoration', 'none');
                    $('#filterUnprocessed').addClass('active').css('text-decoration', 'underline');
                } else {
                    // Clear search and update UI
                    table.column(7).search('').draw(false);
                    $('.filter-link').removeClass('active').css('text-decoration', 'none');
                    $('#filterAll').addClass('active').css('text-decoration', 'underline');
                }

                // Restore date filter
                var dateParam = getUrlParam('date');
                if (dateParam && availableDates.includes(dateParam)) {
                    $('#datePicker').val(dateParam).trigger('change');
                }

                // Restore pagination state after filters are applied
                var startParam = getUrlParam('start');
                var lengthParam = getUrlParam('length');

                if (startParam && lengthParam) {
                    // Set the page length first
                    table.page.len(parseInt(lengthParam)).draw(false);

                    // Calculate and set the page number
                    var pageNumber = Math.floor(parseInt(startParam) / parseInt(lengthParam));
                    table.page(pageNumber).draw(false); // Don't trigger events yet
                }

                // Re-enable URL updates after restoration is complete
                setTimeout(function() {
                    isRestoringState = false;
                }, 100);
            }

            // Call restore function after table is fully initialized
            setTimeout(function() {
                restoreStateFromUrl();
                handleNotificationFromUrl();
            }, 1000); // Further increased delay to ensure DataTable is fully ready

            // Handle notification from URL parameters
            function handleNotificationFromUrl() {
                var notificationParam = getUrlParam('notification');
                var notificationTypeParam = getUrlParam('notification_type') || 'success';

                if (notificationParam) {
                    // Show the notification
                    showTempNotice(decodeURIComponent(notificationParam), notificationTypeParam);

                    // Remove notification parameters from URL after showing
                    setTimeout(function() {
                        removeNotificationFromUrl();
                    }, 4500); // Remove after notification disappears
                }
            }

            // Remove notification parameters from URL
            function removeNotificationFromUrl() {
                var url = new URL(window.location);
                url.searchParams.delete('notification');
                url.searchParams.delete('notification_type');

                // Update URL without page refresh
                window.history.replaceState({}, document.title, url.toString());
            }

            // Add event listeners to preserve URL state
            table.on('page.dt', function() {
                var info = table.page.info();
                updateUrlParams({
                    'start': info.start,
                    'length': info.length
                });
            });

            table.on('length.dt', function(e, settings, len) {
                updateUrlParams({
                    'length': len,
                    'start': 0 // Reset to first page when changing page length
                });
            });

            table.on('search.dt', function() {
                var searchValue = table.search();
                updateUrlParams({
                    'search': searchValue,
                    'start': 0 // Reset to first page when searching
                });
            });

            // Update live time every second (both local and Pacific time)
            function updateLiveTime() {
                const now = new Date();

                // Local time
                const localTime = now.toLocaleString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });

                // Pacific time (Product Hunt timezone)
                const pacificTime = now.toLocaleString('en-US', {
                    timeZone: 'America/Los_Angeles',
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });

                document.getElementById('localTime').textContent = 'Local: ' + localTime;
                document.getElementById('pacificTime').textContent = 'Pacific: ' + pacificTime;
            }

            // Update immediately and then every second
            updateLiveTime();
            setInterval(updateLiveTime, 1000);

            // Fix DataTables header on window resize
            $(window).on('resize', function() {
                $('#productsTable').DataTable().columns.adjust().draw();
            });

            // Filter functionality
            $('#filterAll').click(function(e) {
                e.preventDefault();
                table.column(7).search('').draw(); // Clear search on Links column
                $('.filter-link').removeClass('active').css('text-decoration', 'none');
                $(this).addClass('active').css('text-decoration', 'underline');
                updateUrlParams({ 'filter': null });
            });

            $('#filterFailed').click(function(e) {
                e.preventDefault();
                table.column(7).search('❌').draw(); // Search for ❌ in Links column
                $('.filter-link').removeClass('active').css('text-decoration', 'none');
                $(this).addClass('active').css('text-decoration', 'underline');
                updateUrlParams({ 'filter': 'failed' });
            });

            $('#filterUnprocessed').click(function(e) {
                e.preventDefault();
                table.column(7).search('🕓').draw(); // Search for 🕓 in Links column
                $('.filter-link').removeClass('active').css('text-decoration', 'none');
                $(this).addClass('active').css('text-decoration', 'underline');
                updateUrlParams({ 'filter': 'unprocessed' });
            });

            $('#filterFixed').click(function(e) {
                e.preventDefault();
                table.column(7).search('✓').draw(); // Search for ✓ in Links column
                $('.filter-link').removeClass('active').css('text-decoration', 'none');
                $(this).addClass('active').css('text-decoration', 'underline');
                updateUrlParams({ 'filter': 'fixed' });
            });

            // Set up date picker with available dates
            var datePicker = document.getElementById('datePicker');
            datePicker.min = availableDates[availableDates.length - 1]; // Oldest date
            datePicker.max = availableDates[0]; // Newest date

            // Date filter functionality
            $('#datePicker').change(function() {
                var selectedDate = $(this).val();

                if (selectedDate === '') {
                    // Clear date filter
                    currentDateFilter = null;
                    table.column(5).search('').draw();
                    updateUrlParams({ 'date': null });
                } else if (availableDates.includes(selectedDate)) {
                    currentDateFilter = selectedDate;

                    // Apply date filter to Launch Date column (column 5)
                    var formattedDate = new Date(selectedDate).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });

                    table.column(5).search(formattedDate).draw();
                    updateUrlParams({ 'date': selectedDate });
                } else {
                    // Invalid date selected, clear it
                    $(this).val('');
                    currentDateFilter = null;
                    table.column(5).search('').draw();
                    updateUrlParams({ 'date': null, 'start': 0 });
                    alert('Please select a valid date with available data.');
                }
            });

            // Reset date filter
            $('#resetDateFilter').click(function(e) {
                e.preventDefault();
                $('#datePicker').val('');
                currentDateFilter = null;
                table.column(5).search('').draw();
                updateUrlParams({ 'date': null, 'start': 0 });
            });

            // Keep date filter active when other filters are applied
            $('#filterAll, #filterFailed, #filterFixed, #filterUnprocessed').click(function() {
                if (currentDateFilter) {
                    var formattedDate = new Date(currentDateFilter).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });

                    // Reapply date filter after status filter
                    setTimeout(function() {
                        table.column(5).search(formattedDate).draw();
                    }, 10);
                }
            });
        });

        // Temporary notice functions
        function showTempNotice(message, type = 'success', duration = 4000) {
            const notice = document.getElementById('tempNotice');
            notice.textContent = message;
            notice.className = 'temp-notice ' + type;

            // Show notice
            setTimeout(() => notice.classList.add('show'), 100);

            // Hide notice after duration
            setTimeout(() => {
                notice.classList.remove('show');
                setTimeout(() => {
                    notice.textContent = '';
                    notice.className = 'temp-notice';
                }, 300);
            }, duration);
        }

        // URL state management functions
        var isRestoringState = false; // Flag to prevent URL updates during state restoration

        function updateUrlParams(params) {
            if (isRestoringState) return; // Don't update URL during state restoration

            const url = new URL(window.location);
            Object.keys(params).forEach(key => {
                if (params[key] !== null && params[key] !== '') {
                    url.searchParams.set(key, params[key]);
                } else {
                    url.searchParams.delete(key);
                }
            });
            window.history.replaceState({}, '', url);
        }

        function getUrlParam(param) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        }

        function getCurrentUrlWithParams() {
            return window.location.href;
        }

        // Manual URL popup functions
        function openManualUrlPopup(productId) {
            document.getElementById('popup_product_id').value = productId;
            document.getElementById('manual_url').value = '';
            document.getElementById('manualUrlPopup').style.display = 'block';
            document.getElementById('manual_url').focus();
        }

        function closeManualUrlPopup() {
            document.getElementById('manualUrlPopup').style.display = 'none';
        }

        // Close popup on Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeManualUrlPopup();
            }
        });

        // Handle manual URL form submission
        document.getElementById('manualUrlForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('api/manual_url_api_clean.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeManualUrlPopup();
                    // Redirect with success notification parameter
                    var currentUrl = getCurrentUrlWithParams();
                    var separator = currentUrl.includes('?') ? '&' : '?';
                    window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Manual URL saved successfully!') + '&notification_type=success';
                } else {
                    // Show error notification via URL parameter
                    var currentUrl = getCurrentUrlWithParams();
                    var separator = currentUrl.includes('?') ? '&' : '?';
                    window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Error: ' + data.message) + '&notification_type=error';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showTempNotice('An error occurred while saving the URL.', 'error');
            });
        });

        // Remove manual URL override
        function removeManualUrl(productId) {
            if (!confirm('Are you sure you want to remove the manual URL override?')) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'remove');
            formData.append('product_id', productId);
            formData.append('csrf_token', '<?= htmlspecialchars(Auth::generateCSRFToken(), ENT_QUOTES, 'UTF-8') ?>');

            fetch('api/manual_url_api_clean.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect with success notification parameter
                    var currentUrl = getCurrentUrlWithParams();
                    var separator = currentUrl.includes('?') ? '&' : '?';
                    window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Manual URL override removed successfully!') + '&notification_type=success';
                } else {
                    // Show error notification via URL parameter
                    var currentUrl = getCurrentUrlWithParams();
                    var separator = currentUrl.includes('?') ? '&' : '?';
                    window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Error: ' + data.message) + '&notification_type=error';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showTempNotice('An error occurred while removing the URL override.', 'error');
            });
        }
    </script>
</body>
</html>
