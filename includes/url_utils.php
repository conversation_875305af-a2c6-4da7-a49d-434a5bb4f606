<?php
/**
 * URL Utilities for cleaning and following redirects
 */

class URLUtils {

    // Array of realistic user agents to rotate through (desktop and mobile)
    private static $userAgents = [
        // Desktop Chrome
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',

        // Desktop Firefox
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/121.0',

        // Desktop Safari
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15',

        // Desktop Edge
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',

        // Mobile Chrome (Android)
        'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 11; OnePlus 9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',

        // Mobile Safari (iOS)
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',

        // Mobile Firefox
        'Mozilla/5.0 (Mobile; rv:109.0) Gecko/109.0 Firefox/121.0',
        'Mozilla/5.0 (Android 13; Mobile; rv:109.0) Gecko/109.0 Firefox/121.0'
    ];

    /**
     * Keep URL parameters but ensure URL is properly formatted
     */
    public static function cleanUrl($url) {
        if (empty($url)) {
            return '';
        }

        // Just trim whitespace and return the URL as-is (keeping parameters)
        $url = trim($url);

        // Ensure URL has a scheme
        if (!preg_match('/^https?:\/\//', $url)) {
            $url = 'https://' . $url;
        }

        return $url;
    }
    
    /**
     * Follow redirects and get the final URL
     * Handles multiple redirects with longer timeout, returns empty string for errors
     */
    public static function getFinalUrl($url, $maxRedirects = 10, $timeout = 60) {
        if (empty($url)) {
            return '';
        }

        // Safety precaution: Random delay between 3-6 seconds for Product Hunt domains with jitter
        if (strpos($url, 'producthunt.com') !== false) {
            $baseDelay = rand(7500, 12000); // 7.5 to 12 seconds in milliseconds
            $jitter = rand(-1500, 1500); // Add/subtract up to 1.5 seconds for jitter
            $totalDelay = max(6000, $baseDelay + $jitter); // Ensure minimum 6 seconds
            $delaySeconds = $totalDelay / 1000.0; // Convert to seconds
            echo "⏳ Safety delay: " . number_format($delaySeconds, 2) . "s for Product Hunt domain...\n";
            usleep((int)($totalDelay * 1000)); // Convert to microseconds, cast to int
        }

        // Clean the URL first (but keep parameters)
        $url = self::cleanUrl($url);

        // Special handling for problematic domains
        $isVSMarketplace = strpos($url, 'marketplace.visualstudio.com') !== false;
        $isAppStore = strpos($url, 'apps.apple.com') !== false;

        // Get random user agent
        $userAgent = self::getRandomUserAgent();

        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_NOBODY => true, // HEAD request only - faster
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => $maxRedirects,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_USERAGENT => $userAgent,
            CURLOPT_FORBID_REUSE => false, // Keep-alive connections
            CURLOPT_HEADER => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_AUTOREFERER => true,
            CURLOPT_ENCODING => '', // Accept all encodings
            CURLOPT_HTTPHEADER => self::getBrowserHeaders($isVSMarketplace, $isAppStore)
        ]);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        $redirectCount = curl_getinfo($ch, CURLINFO_REDIRECT_COUNT);

        // If HEAD request failed or returned 405 (Method Not Allowed), try GET request
        if ($result === false || $httpCode == 405 || $httpCode == 403) {
            $wasHeadRequest = true;
            curl_close($ch);

            // Retry with GET request for problematic domains
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_NOBODY => false, // GET request with body
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => $maxRedirects,
                CURLOPT_TIMEOUT => $timeout,
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_USERAGENT => $userAgent,
                CURLOPT_FORBID_REUSE => false, // Keep-alive connections
                CURLOPT_HEADER => false,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_AUTOREFERER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_HTTPHEADER => self::getBrowserHeaders($isVSMarketplace, $isAppStore)
            ]);

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
            $redirectCount = curl_getinfo($ch, CURLINFO_REDIRECT_COUNT);

            if ($wasHeadRequest && $result !== false && $httpCode >= 200 && $httpCode < 400) {
                echo "🔄 HEAD request failed, GET request succeeded\n";
            }
        }

        if ($result === false) {
            $error = curl_error($ch);
            curl_close($ch);

            echo "❌ Error: Could not reach $url - $error\n";
            return ''; // Return empty string for unreachable URLs
        }

        curl_close($ch);

        // If we got a valid response, return the final URL
        if ($httpCode >= 200 && $httpCode < 400) {
            if ($redirectCount > 0) {
                echo "🔄 Followed $redirectCount redirect(s) to final URL\n";
            }
            return $finalUrl ?: '';
        }

        // If we got an error response (4xx, 5xx), return empty string
        echo "❌ Error: HTTP $httpCode for $url\n";
        return '';
    }
    
    /**
     * Batch process URLs with rate limiting to be respectful
     */
    public static function processBatchUrls($urls, $delayMs = 500) {
        $results = [];
        $total = count($urls);
        $processed = 0;

        foreach ($urls as $originalUrl) {
            $processed++;

            echo "Processing URL $processed/$total: " . substr($originalUrl, 0, 60) . "...\n";

            $finalUrl = self::getFinalUrl($originalUrl);
            $results[$originalUrl] = $finalUrl;

            // Longer delay to handle multiple redirects better
            if ($delayMs > 0 && $processed < $total) {
                usleep($delayMs * 1000); // Convert ms to microseconds
            }
        }

        return $results;
    }
    
    /**
     * Validate if a URL is properly formatted
     */
    public static function isValidUrl($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * Check if URL is likely a redirect URL (contains common redirect patterns)
     */
    public static function isLikelyRedirect($url) {
        $redirectPatterns = [
            'producthunt.com/r/',
            'bit.ly/',
            'tinyurl.com/',
            'short.link/',
            'go.', // go.example.com
            'link.',
            'redirect',
            'r.', // r.example.com
            'l.', // l.example.com
        ];
        
        foreach ($redirectPatterns as $pattern) {
            if (strpos($url, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get domain from URL
     */
    public static function getDomain($url) {
        $parsedUrl = parse_url($url);
        return isset($parsedUrl['host']) ? $parsedUrl['host'] : '';
    }
    
    /**
     * Check if two URLs point to the same domain
     */
    public static function isSameDomain($url1, $url2) {
        return self::getDomain($url1) === self::getDomain($url2);
    }

    /**
     * Remove URL parameters and fragments, keeping only the base URL
     */
    public static function removeUrlParameters($url) {
        if (empty($url)) {
            return '';
        }

        $parsedUrl = parse_url($url);
        if (!$parsedUrl) {
            return $url; // Return original if parsing fails
        }

        // Rebuild URL without query parameters and fragments
        $cleanUrl = '';

        if (isset($parsedUrl['scheme'])) {
            $cleanUrl .= $parsedUrl['scheme'] . '://';
        }

        if (isset($parsedUrl['host'])) {
            $cleanUrl .= $parsedUrl['host'];
        }

        if (isset($parsedUrl['port'])) {
            $cleanUrl .= ':' . $parsedUrl['port'];
        }

        if (isset($parsedUrl['path'])) {
            $cleanUrl .= $parsedUrl['path'];
        }

        return $cleanUrl;
    }

    /**
     * Get a random user agent from the pool
     */
    private static function getRandomUserAgent() {
        return self::$userAgents[array_rand(self::$userAgents)];
    }

    /**
     * Get browser headers optimized for specific domains
     */
    private static function getBrowserHeaders($isVSMarketplace = false, $isAppStore = false) {
        $baseHeaders = [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language: en-US,en;q=0.9',
            'Accept-Encoding: gzip, deflate, br',
            'DNT: 1',
            'Connection: keep-alive',
            'Upgrade-Insecure-Requests: 1',
            'Cache-Control: max-age=0'
        ];

        if ($isVSMarketplace) {
            // VS Marketplace specific headers
            $baseHeaders[] = 'Referer: https://code.visualstudio.com/';
            $baseHeaders[] = 'Sec-Fetch-Dest: document';
            $baseHeaders[] = 'Sec-Fetch-Mode: navigate';
            $baseHeaders[] = 'Sec-Fetch-Site: cross-site';
            $baseHeaders[] = 'Sec-Fetch-User: ?1';
        } elseif ($isAppStore) {
            // App Store specific headers
            $baseHeaders[] = 'Referer: https://www.apple.com/';
            $baseHeaders[] = 'Sec-Fetch-Dest: document';
            $baseHeaders[] = 'Sec-Fetch-Mode: navigate';
            $baseHeaders[] = 'Sec-Fetch-Site: same-site';
            $baseHeaders[] = 'Sec-Fetch-User: ?1';
        } else {
            // Default headers
            $baseHeaders[] = 'Sec-Fetch-Dest: document';
            $baseHeaders[] = 'Sec-Fetch-Mode: navigate';
            $baseHeaders[] = 'Sec-Fetch-Site: none';
            $baseHeaders[] = 'Sec-Fetch-User: ?1';
        }

        return $baseHeaders;
    }
}
?>
